import { lazy, Suspense, useCallback, useEffect } from 'react';
import { Loader } from '@components/ui/loader/loader';
import {
    createBrowserRouter,
    createRoutesFromElements,
    Navigate,
    Route,
    RouterProvider,
} from 'react-router-dom';
import { SettingsManager, TCredentials } from '@classes/settingsManager';
import { InvitationProfileUseCases } from './invitations/invitationProfile';
import { SimulationListUseCases } from '@components/simulations/simulationList';
import { PusherProvider } from '@common/contexts/Pusher';
import { useReactive } from 'ahooks';
import { CRMAPI } from '@api/crmApi';
import { rootStore } from '@store/instanse';
import { Button, ConfigProvider, message, Row } from 'antd';
import ruRU from 'antd/locale/ru_RU';
import { observer } from 'mobx-react';
import { ConstructorScheduleUseCases } from './constructor/constructorSchedule/constructorSchedule';
import { LayoutWrapper } from './ui/layout/uniLayout';
import { ProtectedRoute } from './protectedRoutes/protectedRoutes';
import { Permissions } from '@classes/permissions';
import { permission } from 'process';
import { simulationsRoutes } from 'src/routes/dashboard/simulations';
import { constructorRoutes } from 'src/routes/dashboard/constructor';
import { managementRoutes } from 'src/routes/dashboard/management';
import { controlsRoutes } from 'src/routes/dashboard/controls';
import { testsRoutes } from 'src/routes/dashboard/tests';
import { router } from 'src/routes';

const FontsTest = lazy(() => import('@components/tests/fonts'));
const ColorsTest = lazy(() => import('@components/tests/colors'));
const StoresTest = lazy(() => import('@components/tests/stores'));
const NetworkTest = lazy(() => import('@components/tests/network'));
const TableStatusesTest = lazy(() => import('@components/tests/tableStatuses'));

const Login = lazy(() => import('@components/login/login'));
const Logout = lazy(() => import('@components/user/logout'));
const InviteRegistration = lazy(() => import('@components/inviteRegistration/inviteRegistration'));

const SimulationList = lazy(() => import('@components/simulations/simulationList'));
const SimulationProfile = lazy(() => import('@components/simulations/simulationProfile'));

const UserList = lazy(() => import('@components/user/userList'));
const UserProfile = lazy(() => import('@components/user/userProfile'));
const InvitationList = lazy(() => import('@components/invitations/invitationList'));
const InvitationProfile = lazy(() => import('@components/invitations/invitationProfile'));
const RolesPage = lazy(() => import('@components/roles/roles'));
const FiltersPage = lazy(() => import('@components/filters/filters'));

const ConstructorSim = lazy(() => import('@components/constructor/constructorSim/constructorSim'));
const ConstructorGraph = lazy(
    () => import('@components/constructor/constructorGraph/constructorGraph'),
);
const ConstructorGantt = lazy(
    () => import('@components/constructor/constructorGantt/constructorGantt'),
);
const ConstructorNodeList = lazy(
    () => import('@components/constructor/constructorNode/constructorNodeList'),
);
const ConstructorNodeProfile = lazy(
    () => import('@components/constructor/constructorNode/constructorNodeProfile'),
);
const ConstructorWorkerList = lazy(
    () => import('@components/constructor/constructorWorker/constructorWorkerList'),
);
const ConstructorWorkerProfile = lazy(
    () => import('@components/constructor/constructorWorker/constructorWorkerProfile'),
);
const ConstructorEventList = lazy(
    () => import('@components/constructor/constructorEvent/constructorEventList'),
);
const ConsturctorEventProfile = lazy(
    () => import('@components/constructor/constructorEvent/constructorEventProfile'),
);
const ConstructorSchedule = lazy(
    () => import('@components/constructor/constructorSchedule/constructorSchedule'),
);

const SessionList = lazy(() => import('@components/sessions/sessionList'));
const SessionProfile = lazy(() => import('@components/sessions/sessionProfile'));
const SessionAssignmentList = lazy(
    () => import('@components/sessions/sessionAssignments/sessionAssignmentList'),
);
const SessionAssignmentProfile = lazy(
    () => import('@components/sessions/sessionAssignments/sessionAssignmentProfile'),
);

const SessionDesktop = lazy(() => import('@components/ingame/sessionDesktop/sessionDesktop'));
const SessionDashboard = lazy(() => import('@components/ingame/sessionDashboard/sessionDashboard'));
const SessionGantt = lazy(() => import('@components/ingame/sessionGantt/sessionGantt'));
const SessionChats = lazy(() => import('@components/ingame/sessionChats/sessionChats'));
const SessionCalendar = lazy(() => import('@components/ingame/sessionCalendar/sessionCalendar'));
const SessionBudget = lazy(() => import('@components/ingame/sessionBudget/sessionBudget'));
const SessionWorkerList = lazy(() => import('@components/ingame/sessionWorkers/sessionWorkerList'));
const SessionWorkerProfile = lazy(
    () => import('@components/ingame/sessionWorkers/sessionWorkerProfile'),
);
const SessionTask = lazy(() => import('@components/ingame/sessionTask/sessionTask'));
const SessionGraph = lazy(() => import('@components/ingame/ingameGraph/ingameGraph'));
const SessionCharts = lazy(() => import('@components/ingame/charts/chartList'));
const ComplexTaskChartPage = lazy(
    () => import('@components/ingame/charts/complexTaskChart/complexTaskChartPage'),
);

const NotificationHolder = lazy(
    () => import('@components/notifications/notificationChannelHolder'),
);
const ChatsPage = lazy(() => import('@components/chats/chatsPage'));
const NotificationList = lazy(() => import('@components/notifications/notificationList'));

type TState = {
    accessToken: TCredentials['accessToken'];
    assignmentNoticeShown: boolean;
    validated: boolean;
    user_id: TCredentials['user_id'];
};

export interface RouteHandle {
    additionalClass?: string;
}

const App = observer((): JSX.Element => {
    const state = useReactive<TState>({
        accessToken: null,
        assignmentNoticeShown: false,
        validated: false,
        user_id: null,
    });

    const validateToken = useCallback(async () => {
        try {
            const creds = SettingsManager.getConnectionCredentials();
            if (creds?.accessToken == null) return;
            const api = new CRMAPI(creds?.accessToken);
            const result = await api.currentUser(true);
            if (result.errorMessages) throw result;
        } catch (errors) {
            if (rootStore.socketStore.verbose) {
                console.log(errors);
            }
            SettingsManager.clearConnectionCredentials();
            rootStore.socketStore.clearStore();
        }
    }, []);

    useEffect(() => {
        if (!state.validated) {
            validateToken().then(() => {
                state.validated = true;
            });
        }
    }, []);

    useEffect(() => {
        const creds = SettingsManager.getConnectionCredentials();
        if (state.accessToken != creds?.accessToken) {
            state.accessToken = creds?.accessToken;
        }
        if (state.user_id != creds?.user_id) {
            state.user_id = creds?.user_id;
        }
    }, [rootStore.currentUserStore.user]);

    function getContent(): JSX.Element {
        if (SettingsManager.getConnectionCredentials()?.accessToken == null) {
            if (
                window.location?.pathname != '/login' &&
                !window.location.pathname.includes('/invite/')
            ) {
                window.location.pathname = '/login';
            }
        } else {
            if (window.location?.pathname == '/login') {
                window.location.pathname = '/lk';
            }
            const creds = SettingsManager.getConnectionCredentials();
            if (
                creds.sessionAssignmentId != null &&
                window.location?.pathname.includes('/session') == false &&
                !state.assignmentNoticeShown
            ) {
                message.destroy('assignment-notice');
                state.assignmentNoticeShown = true;
                message.info({
                    content: (
                        <Row style={{ columnGap: '8px', justifyContent: 'space-between' }}>
                            <span className="p4">Найдено прохождение</span>
                            <Button
                                onClick={() => {
                                    message.destroy('assignment-notice');
                                    window.location.pathname = '/session';
                                }}
                                size="small"
                            >
                                Вернуться
                            </Button>
                            <Button
                                danger
                                onClick={() => {
                                    message.destroy('assignment-notice');
                                    SettingsManager.updateConnectionCredentials({
                                        sessionAssignmentId: null,
                                    });
                                }}
                                size="small"
                            >
                                Закрыть
                            </Button>
                        </Row>
                    ),
                    duration: 0,
                    key: 'assignment-notice',
                });
            }
            if (
                creds.sessionAssignmentId != null &&
                window.location?.pathname.includes('/session') &&
                state.assignmentNoticeShown
            ) {
                state.assignmentNoticeShown = false;
            }
            if (creds.sessionAssignmentId == null) {
                message.destroy('assignment-notice');
            }
        }
        if (!state.validated) return <Loader />;

        return (
            <Suspense>
                <RouterProvider router={router} />
            </Suspense>
        );
    }

    return (
        <ConfigProvider locale={ruRU}>
            <PusherProvider
                userId={state.validated ? state.user_id : null}
                token={state.validated ? state.accessToken : null}
            >
                <div id="simbios-app-content">
                    <NotificationHolder />
                    {getContent()}
                </div>
            </PusherProvider>
        </ConfigProvider>
    );
});

export { App };
