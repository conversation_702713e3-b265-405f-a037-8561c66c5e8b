import ColorsTest from '@components/tests/colors';
import FontsTest from '@components/tests/fonts';
import StoresTest from '@components/tests/stores';
import TableStatusesTest from '@components/tests/tableStatuses';
import { lazy } from 'react';
import { Route } from 'react-router-dom';
const NetworkTest = lazy(() => import('@components/tests/network'));

export const testsRoutes = (
    <>
        <Route
            path="tests/fonts"
            element={<FontsTest />}
        />
        <Route
            path="tests/colors"
            element={<ColorsTest />}
        />
        <Route
            path="tests/stores"
            element={<StoresTest />}
        />
        <Route
            path="tests/network"
            element={<NetworkTest />}
        />
        <Route
            path="tests/table-statuses"
            element={<TableStatusesTest />}
        />
    </>
);
