import FiltersPage from '@components/filters/filters';
import InvitationList from '@components/invitations/invitationList';
import InvitationProfile, {
    InvitationProfileUseCases,
} from '@components/invitations/invitationProfile';
import { ProtectedRoute } from '@components/protectedRoutes/protectedRoutes';
import RolesPage from '@components/roles/roles';
import UserList from '@components/user/userList';
import UserProfile from '@components/user/userProfile';
import { Route } from 'react-router-dom';
import { Permissions } from '@classes/permissions';

export const managementRoutes = (
    <>
        <Route element={<ProtectedRoute requiredPermissions={[Permissions.UserList]} />}>
            <Route
                path="management/users"
                element={<UserList />}
            />
        </Route>
        <Route
            path="management/users/:userId"
            element={<UserProfile />}
        />
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.UserList,
                        Permissions.UserGet,
                        Permissions.InvitationBulkAdd,
                        Permissions.InvitationBulkResult,
                        Permissions.InvitationGet,
                        Permissions.InvitationUpdate,
                        Permissions.InvitationCreate,
                        Permissions.InvitationRestore,
                        Permissions.InvitationDelete,
                        Permissions.InvitationList,
                    ]}
                />
            }
        >
            <Route
                path="management/invitations"
                element={<InvitationList />}
            />
        </Route>
        <Route element={<ProtectedRoute requiredPermissions={[Permissions.InvitationCreate]} />}>
            <Route
                path="management/invitations/:invitationId"
                element={<InvitationProfile useCase={InvitationProfileUseCases.Profile} />}
            />
            <Route
                path="management/invitations/new"
                element={<InvitationProfile useCase={InvitationProfileUseCases.New} />}
            />
        </Route>
        <Route
            element={
                <ProtectedRoute
                    requiredPermissions={[
                        Permissions.RoleList,
                        Permissions.PermissionCategoryList,
                    ]}
                />
            }
        >
            <Route
                path="management/roles"
                element={<RolesPage />}
            />
            <Route
                path="management/filters"
                element={<FiltersPage />}
            />
        </Route>
    </>
);
