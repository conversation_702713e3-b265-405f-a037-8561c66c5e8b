import { ProtectedRoute } from '@components/protectedRoutes/protectedRoutes';
import SimulationList, { SimulationListUseCases } from '@components/simulations/simulationList';
import SimulationProfile from '@components/simulations/simulationProfile';
import { Navigate, Route } from 'react-router-dom';
import { Permissions } from '@classes/permissions';

export const simulationsRoutes = (
    <>
        <Route
            path="simulations"
            element={<Navigate to="/simulations/finished" />}
        />
        <Route element={<ProtectedRoute requiredPermissions={[Permissions.SimulationList]} />}>
            <Route
                path="simulations/finished"
                element={<SimulationList useCase={SimulationListUseCases.Finished} />}
            />
            <Route
                path="simulations/unfinished"
                element={<SimulationList useCase={SimulationListUseCases.Unfinished} />}
            />
            <Route
                path="simulations/archive"
                element={<SimulationList useCase={SimulationListUseCases.Archive} />}
            />
            <Route
                path="simulations/all"
                element={<SimulationList useCase={SimulationListUseCases.All} />}
            />
        </Route>
        <Route element={<ProtectedRoute requiredPermissions={[Permissions.SimulationGet]} />}>
            <Route
                path="simulations/:simId"
                element={<SimulationProfile />}
            />
        </Route>
    </>
);
