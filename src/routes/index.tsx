import ChatsPage from '@components/chats/chatsPage';
import ComplexTaskChartPage from '@components/ingame/charts/complexTaskChart/complexTaskChartPage';
import SessionBudget from '@components/ingame/sessionBudget/sessionBudget';
import SessionCalendar from '@components/ingame/sessionCalendar/sessionCalendar';
import SessionChats from '@components/ingame/sessionChats/sessionChats';
import SessionDashboard from '@components/ingame/sessionDashboard/sessionDashboard';
import SessionDesktop from '@components/ingame/sessionDesktop/sessionDesktop';
import SessionGantt from '@components/ingame/sessionGantt/sessionGantt';
import SessionTask from '@components/ingame/sessionTask/sessionTask';
import SessionWorkerList from '@components/ingame/sessionWorkers/sessionWorkerList';
import SessionWorkerProfile from '@components/ingame/sessionWorkers/sessionWorkerProfile';
import InviteRegistration from '@components/inviteRegistration/inviteRegistration';
import Login from '@components/login/login';
import NotificationList from '@components/notifications/notificationList';
import { ProtectedRoute } from '@components/protectedRoutes/protectedRoutes';
import { LayoutWrapper } from '@components/ui/layout/uniLayout';
import Logout from '@components/user/logout';
import UserProfile from '@components/user/userProfile';
import { createBrowserRouter, createRoutesFromElements, Navigate, Route } from 'react-router-dom';
import { constructorRoutes } from './dashboard/constructor';
import { controlsRoutes } from './dashboard/controls';
import { managementRoutes } from './dashboard/management';
import { simulationsRoutes } from './dashboard/simulations';
import { testsRoutes } from './dashboard/tests';
import { lazy } from 'react';
const SessionCharts = lazy(() => import('@components/ingame/charts/chartList'));
const SessionGraph = lazy(() => import('@components/ingame/ingameGraph/ingameGraph'));
import { Permissions } from '@classes/permissions';

export const router = () => {
    return createBrowserRouter(
        createRoutesFromElements(
            <Route path="/">
                <Route
                    path="login"
                    element={<Login />}
                />
                <Route
                    path="invite/:inviteUid"
                    element={<InviteRegistration />}
                />
                <Route element={<LayoutWrapper />}>
                    <Route
                        index
                        element={<Navigate to="/lk" />}
                    />
                    {simulationsRoutes}
                    {constructorRoutes}
                    {managementRoutes}
                    {controlsRoutes}
                    {testsRoutes}
                    <Route
                        element={
                            <ProtectedRoute requiredPermissions={[Permissions.NotificationList]} />
                        }
                    >
                        <Route
                            path="notifications"
                            element={<NotificationList />}
                        />
                    </Route>
                    <Route
                        path="chats"
                        element={<ChatsPage />}
                    />
                    <Route
                        path="chats/:chatId"
                        element={<ChatsPage />}
                    />
                    <Route
                        path="lk"
                        element={<UserProfile />}
                    />
                    <Route
                        path="logout"
                        element={<Logout />}
                    />
                    <Route
                        path="*"
                        element={<Navigate to="lk" />}
                    />
                </Route>

                <Route
                    path="session"
                    element={<Navigate to="/session/desktop" />}
                />
                <Route
                    path="session/desktop"
                    element={<SessionDesktop />}
                />
                <Route
                    path="session/dashboard"
                    element={<SessionDashboard />}
                />
                <Route
                    path="session/gantt"
                    element={<SessionGantt />}
                />
                <Route
                    path="session/chats"
                    element={<SessionChats />}
                />
                <Route
                    path="session/chats/:chatId"
                    element={<SessionChats />}
                />
                <Route
                    path="session/calendar"
                    element={<SessionCalendar />}
                />
                <Route
                    path="session/budget"
                    element={<SessionBudget />}
                />
                <Route
                    path="session/tasks/:sessionTaskId"
                    element={<SessionTask />}
                />
                <Route
                    path="session/workers"
                    element={<SessionWorkerList />}
                />
                <Route
                    path="session/workers/:sessionWorkerId"
                    element={<SessionWorkerProfile />}
                />
                <Route
                    path="session/graph"
                    element={<SessionGraph />}
                />
                <Route
                    path="session/charts"
                    element={<SessionCharts />}
                />
                <Route
                    path="sessions/charts/complex-task-chart"
                    element={<ComplexTaskChartPage />}
                />
            </Route>,
        ),
    );
};
